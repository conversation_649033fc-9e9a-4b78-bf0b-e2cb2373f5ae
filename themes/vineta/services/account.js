import { api } from '@/lib/api/client';

export const accountRequests = {
  // Kullanıcı bilgilerini getir
  getUserProfile: () => api.get('/account/profile'),

  // Profil güncelle
  updateProfile: (data) => api.put('/account/profile', data),

  getAddresses: (customerId) => api.get(`/account/${customerId}/addresses`),
  updateAddress: (address) => api.put(`/account/addresses`, address),
  // Adres ekle
  addAddress: (address) => api.post(`/account/${address.customerId}/addresses`, address),

  // Adres ekle
  removeAddress: (addressId) => api.delete(`/account/addresses/${addressId}`),

  // Siparişleri getir
  getOrders: () => api.get('/account/orders'),

  // Sipariş detayını getir
  getOrderById: (orderId) => api.get(`/orders/${orderId}`)
};

export const customerRequests = {
  // Customer profil bilgilerini getir
  getProfile: () => api.getApiResponse('/customer/profile'),

  // Customer profil güncelle
  updateProfile: (data) => api.putApiResponse('/customer/profile', data),

  // Customer şifre güncelle
  updatePassword: (data) => api.putApiResponse('/customer/password', data),

  // Customer hesap pasifleştir
  deactivateAccount: (data) => api.postApiResponse('/customer/deactivate', data)
};


const wishlistRequests = {
  // İstek listesini getir
  getWishlist: () => api.get('/account/wishlist'),

  // Ürün ekle
  addToWishlist: (productId) =>
    api.post('/account/wishlist/product', { productId }),

  // Ürün çıkar
  removeFromWishlist: (productId) =>
    api.delete(`/account/wishlist/product/${productId}`)
};
