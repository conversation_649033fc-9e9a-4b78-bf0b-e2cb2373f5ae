import axios from 'axios';
import { getSession } from 'next-auth/react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Create a base axios instance with default configuration for B2C customer APIs
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:33800/web-api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  async (config) => {
    if (typeof window !== 'undefined') {
      const session = await getSession();
      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
    } else {
      try {
        const session = await getServerSession(authOptions);
        if (session?.accessToken) {
          config.headers.Authorization = `Bearer ${session.accessToken}`;
        }
      } catch (error) {
        console.error('Server-side session error:', error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors and ApiResponse format
apiClient.interceptors.response.use(
  (response) => {
    // Check if this is an ApiResponse method call
    const isApiResponseMethod = response.config.url && response.config._isApiResponseMethod;
    
    // Handle ApiResponse format
    if (response.data && typeof response.data === 'object' && 'success' in response.data) {
      if (response.data.success) {
        // For ApiResponse methods, return full response
        if (isApiResponseMethod) {
          return response;
        }
        // For regular methods, return the data field for successful ApiResponse
        return { ...response, data: response.data.data };
      } else {
        // Throw error for failed ApiResponse
        const error = new Error(response.data.message || 'API Error');
        error.response = response;
        error.apiResponse = response.data;
        throw error;
      }
    }

    // Return original response if not ApiResponse format
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      if (typeof window !== 'undefined') {
        const { signOut } = await import('next-auth/react');
        const { toast } = await import('sonner');

        console.log('API 401 error, signing out...');
        toast.error('Oturumunuz sonlandırıldı. Lütfen tekrar giriş yapın.');

        await signOut({
          redirect: false,
          callbackUrl: '/login',
        });

        return Promise.reject(new Error('Session terminated'));
      }
    }

    return Promise.reject(error);
  }
);

// Generic API request methods
export const api = {
  get: (url, params) =>
    apiClient.get(url, { params }).then((res) => res.data),

  post: (url, data) =>
    apiClient.post(url, data).then((res) => res.data),

  put: (url, data) =>
    apiClient.put(url, data).then((res) => res.data),

  patch: (url, data) =>
    apiClient.patch(url, data).then((res) => res.data),

  delete: (url) =>
    apiClient.delete(url).then((res) => res.data),

  getApiResponse : (url, params) =>
    apiClient.get(url, { params, _isApiResponseMethod: true }).then((res) => res.data),

  postApiResponse : (url, data) =>
    apiClient.post(url, data, { _isApiResponseMethod: true }).then((res) => res.data),

  putApiResponse : (url, data) =>
    apiClient.put(url, data, { _isApiResponseMethod: true }).then((res) => res.data),
  
  patchApiResponse : (url, data) =>
    apiClient.patch(url, data, { _isApiResponseMethod: true }).then((res) => res.data),
    
  deleteApiResponse : (url) =>
    apiClient.delete(url, { _isApiResponseMethod: true }).then((res) => res.data),
  
  
  postFormData: (url, formData) => {
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    return apiClient.post(url, formData, config).then((res) => res.data);
  },
};

export default api;
