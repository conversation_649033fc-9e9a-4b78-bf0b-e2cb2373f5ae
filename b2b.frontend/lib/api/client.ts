import axios from 'axios';
import { getSession } from 'next-auth/react';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';

// Create a base axios instance with default configuration
export const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:33800/admin-api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for adding auth token
apiClient.interceptors.request.use(
  async (config) => {
    // Client-side (browser)
    if (typeof window !== 'undefined') {
      const session = await getSession();

      if (session?.accessToken) {
        config.headers.Authorization = `Bearer ${session.accessToken}`;
      }
    }
    // Server-side (Next.js server components/API routes)
    else {
      try {
        // Server-side session eri<PERSON><PERSON>i i<PERSON>in getServerSession kullanın
        const session = await getServerSession(authOptions);

        if (session?.accessToken) {
          config.headers.Authorization = `Bearer ${session.accessToken}`;
        }
      } catch (error) {
        console.error('Server-side session error:', error);
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for handling common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    // Handle 401 Unauthorized errors (token expired or backend restarted)
    if ((error.response?.status === 401 || error.status === 401) && !originalRequest._retry) {
      originalRequest._retry = true;
      // Use NextAuth signOut instead of direct redirect
      if (typeof window !== "undefined") {
        const { signOut } = await import("next-auth/react");
        const { toast } = await import("sonner");

        console.log("API 401 error, signing out...");
        toast.error("Oturumunuz sonlandırıldı. Lütfen tekrar giriş yapın.");

        await signOut({
          redirect: true,
          callbackUrl: "/login",
        });

        // Prevent further request retries
        return Promise.reject(new Error("Session terminated"));
      }
    }

    return Promise.reject(error);
  }
);

// Generic API request methods
export const api = {
  get: <T>(url: string, params?: unknown) =>
    apiClient.get<T>(url, { params }).then((res) => res.data),

  post: <T>(url: string, data?: unknown) =>
    apiClient.post<T>(url, data).then((res) => res.data),

  put: <T>(url: string, data?: unknown) =>
    apiClient.put<T>(url, data).then((res) => res.data),

  patch: <T>(url: string, data?: unknown) =>
    apiClient.patch<T>(url, data).then((res) => res.data),

  delete: <T>(url: string) =>
    apiClient.delete<T>(url).then((res) => res.data),

  // Delete with body için özel metod
  deleteWithBody: <T>(url: string, data?: unknown) =>
    apiClient.delete<T>(url, { data }).then((res) => res.data),

  // File upload için özel metod
  postFormData: <T>(url: string, formData: FormData) => {
    // FormData için Content-Type header'ını kaldır, axios otomatik ayarlayacak
    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    };
    return apiClient.post<T>(url, formData, config).then((res) => res.data);
  },
};

export default api;
