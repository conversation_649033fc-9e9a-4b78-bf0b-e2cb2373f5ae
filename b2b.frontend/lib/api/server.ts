import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth/auth-options';
import axios from 'axios';
import { redirect } from 'next/navigation';

// Server-side API client
const createServerApiClient = async () => {
  const session = await getServerSession(authOptions);

  const apiClient = axios.create({
    baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:33800/admin-api',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Token varsa ekle
  if (session?.accessToken) {
    apiClient.defaults.headers.common['Authorization'] = `Bearer ${session.accessToken}`;
  }
  // Bu interceptor, API'den gelen tüm yanıtları (başarılı veya hatalı) kontrol eder.
  apiClient.interceptors.response.use(
    // Başarılı yanıtlar için (2xx status code) bir <PERSON>ey ya<PERSON>, do<PERSON><PERSON>an devam etsin.
    (response) => response,
    // Hatalı yanıtlar için bu fonksiyon çalışır.
    async (error) => {
      // Hatanın bir yanıtı var mı ve status kodu 401 mi kontrol et
      if (error.response?.status === 401) {
        console.log('Interceptor: API 401 hatası yakalandı. Oturum temizlenip yönlendiriliyor...');


        // Giriş sayfasına yönlendir
        redirect('/api/auth/signout?callbackUrl=/login');
      }

      // 401 dışındaki diğer tüm hatalar için, hatanın devam etmesine izin ver
      // ki çağıran fonksiyon (örneğin serverApi.get) bunu yakalayabilsin.
      return Promise.reject(error);
    }
  );
  return apiClient;
};

// Server-side API functions
export const serverApi = {
  get: async <T>(url: string, params?: unknown): Promise<T | undefined> => {
    const client = await createServerApiClient();

    try {
      const response = await client.get<T>(url, { params });
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        console.log('API 401 error, clearing session and redirecting to login...');
        redirect('/login');
      }
      throw error;
    }
  },

  post: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    try {
      const response = await client.post<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        console.log('API 401 error, clearing session and redirecting to login...');
        redirect('/login');
      }
      throw error;
    }
  },

  put: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    try {
      const response = await client.put<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
        redirect('/login');
      }
      throw error;
    }
  },

  patch: async <T>(url: string, data?: unknown): Promise<T> => {
    const client = await createServerApiClient();
    try {
      const response = await client.patch<T>(url, data);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {
      }
      throw error;
    }
  },

  delete: async <T>(url: string): Promise<T> => {
    const client = await createServerApiClient();
    try {
      const response = await client.delete<T>(url);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 401) {

        redirect('/login');
      }
      throw error;
    }
  },

  // File upload için özel metod
  postFormData: async <T>(url: string, formData: FormData): Promise<T> => {
    const client = await createServerApiClient();
    // FormData için Content-Type header'ını kaldır, axios otomatik ayarlayacak
    delete client.defaults.headers['Content-Type'];
    const response = await client.post<T>(url, formData);
    return response.data;
  },
};
