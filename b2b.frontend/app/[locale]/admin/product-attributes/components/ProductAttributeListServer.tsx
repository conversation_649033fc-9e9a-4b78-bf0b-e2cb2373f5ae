"use server";

import { Suspense } from "react";
import ProductAttributeListClient from "./ProductAttributeListClient";
import { ProductAttributeDto } from "../types";
import { CategoryDto } from "../../product-categories/types";
import { serverApi as api } from "@/lib/api/server";

interface ProductAttributeListServerProps {
    canRead: boolean;
    canCreate: boolean;
    canUpdate: boolean;
    canDelete: boolean;
}

async function getProductAttributes(): Promise<ProductAttributeDto[]> {
    const response = await api.get<ProductAttributeDto[]>("/productattribute");
    return response;
}

async function getCategories(): Promise<CategoryDto[]> {
    const response = await api.get<CategoryDto[]>("/productcategory");
    return response;
}

export default async function ProductAttributeListServer({ canRead, canCreate, canUpdate, canDelete }: ProductAttributeListServerProps) {
    const [attributes, categories] = await Promise.all([getProductAttributes(), getCategories()]);

    return (
        <Suspense fallback={<div>Loading product attributes...</div>}>
            <ProductAttributeListClient attributes={attributes} categories={categories} canRead={canRead} canCreate={canCreate} canUpdate={canUpdate} canDelete={canDelete} />
        </Suspense>
    );
}
