import { NextRequest, NextResponse } from 'next/server';
import { getToken } from 'next-auth/jwt';
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { cookies } from 'next/headers';

// Bu satırı silebilirsin, middleware (server-side) tarafında 'next-auth/react' kullanılmaz.
// import { signOut } from 'next-auth/react'; 

const intlMiddleware = createIntlMiddleware(routing);

export default async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  const baseUrl = request.nextUrl.origin;
  const sessionTokenName = 'b2b-panel.session-token'; // <PERSON>zel adınızı buraya yazın
  const response = intlMiddleware(request); // Internationalization'ı en başta uygula

  // Eğer requestten 401 gelen bir istekse, session'ı temizle ve login'e yönlendir.
  if (request.headers.get('x-error') === 'Unauthorized') {
    (await cookies()).set(sessionTokenName, '', { maxAge: 0 });
  }
  
  // Not: Bu kısım projenin mantığına göre değişebilir.
  // Projende /admin ve /login dışında bir sayfa yoksa bu mantıklı.
  if (!pathname.includes("/admin") && !pathname.includes("/login")) {
    const url = new URL('/login', baseUrl);
    return NextResponse.redirect(url);

  }

  // Admin sayfaları için yetki kontrolü
  if (pathname.includes("/admin")) {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_PANEL,
      cookieName: sessionTokenName,
    });

    // token yoksa veya token içinde 'roles' alanı yoksa login'e yönlendir.
    // Bu kontrol, backend yeniden başladığında token'dan 'roles' silindiği durumu yakalar.
    const roles = token?.roles as string[] || [];

    if (!token || roles.length === 0) {
      const loginUrl = new URL(`/login`, baseUrl);
      // Admin sayfasından geliniyorsa, nereden geldiğini callback olarak ekleyebilirsin.
      // Bu sayede kullanıcı giriş yapınca doğrudan o sayfaya yönlenir.
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Gerekli roller yoksa anasayfaya veya yetkiniz yok sayfasına yönlendir.
    // Bu kontrol artık güvenli çünkü 'roles' hiçbir zaman undefined olmayacak.
    if (!roles.includes("Admin") && !roles.includes("Developer")) {
      const unauthorizedUrl = new URL(`/`, baseUrl); // veya /unauthorized gibi bir sayfa
      return NextResponse.redirect(unauthorizedUrl);
    }
  }

  // Login sayfası için kontrol
  if (pathname.includes("/login")) {
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET_PANEL,
      cookieName: sessionTokenName,
    });

    // Eğer geçerli bir token ve rolü varsa, login sayfasında durmasına gerek yok.
    if (token) {
      const roles = token.roles as string[] || [];
      if (roles.includes("Admin") || roles.includes("Developer")) {
        const url = new URL(`/admin/dashboard`, baseUrl);
        return NextResponse.redirect(url);
      }

    }
  }

  return response;
}

export const config = {
  matcher: '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
};

